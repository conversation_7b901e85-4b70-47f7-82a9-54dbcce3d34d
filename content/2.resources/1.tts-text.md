---
title: Text to Speech
description: Generate lifelike speech from text in multiple languages and voices.
---

The GeminiGen.AI Text-to-Speech API allows you to convert text into high-quality, natural-sounding speech. You can use this API to generate voiceovers for multimedia content, create narrations for e-books and documents, or turn subtitles into engaging audio experiences.

## Text To Speech
`POST https://api.geminigen.ai/uapi/v1/text-to-speech`

This endpoint allows you to convert text into speech. You can customize the voice, speed, and model used for the conversion.


### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.geminigen.ai/uapi/v1/text-to-speech \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "model": "tts-flash",
    "voices":[
      {
         "name":"Gacrux",
         "voice":{
            "id":"GM013",
            "name":"Gacrux"
         }
      }
   ],
    "speed": 1,
    "input": "Hello, my name is GeminiGen.AI. I am a text-to-speech model.",
    "output_format": "mp3"
  }'
```

```ts [py]
import requests

url = "https://api.geminigen.ai/uapi/v1/text-to-speech"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "model": "tts-flash",
    "voices":[
      {
         "name":"Gacrux",
         "voice":{
            "id":"GM013",
            "name":"Gacrux"
         }
      }
   ],
    "speed": 1,
    "input": "Hello world!",
    "output_format": "mp3"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/text-to-speech";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "model": "tts-flash",
    "voices":[
      {
         "name":"Gacrux",
         "voice":{
            "id":"GM013",
            "name":"Gacrux"
         }
      }
   ],
    "speed": 1,
    "input": "Hello world!",
    "output_format": "mp3"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes
<!-- model	string	chỉ có thể chọn tts-1 hoặc tts-1-hd, mặc định là tts-1	
voice_id	string	tham khảo danh sách voice id ở sheet bên cạnh, mặc định là OA001	
speed	float	từ 1-4, mặc định là 1	
input*	string	max 10000 chars	 -->
<!-- space -->

`model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion. You can choose between `tts-flash` and `tts-pro`. The default value is `tts-flash`.

`voices` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

An array of voice objects used for the conversion. Each voice object contains a name and voice details with id and name. You can find the list of available voices in the [Voice Library](https://geminigen.ai/app/speech-gen).

`speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech. The value should be between 1 and 4. The default value is 1.

`input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text to be converted into speech. The maximum length is 10,000 characters.

`output_format` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The audio format for the generated output. Supported formats include `mp3` and `wav`. Default is `mp3`.

### Example Response
```json [Response]
{
   "success":true,
   "result":{
      "id":1702,
      "uuid":"fee4c104-8f07-11f0-a8a1-d20de1b4070b",
      "user_id":20,
      "model_name":"tts-flash",
      "input_text":"Hello, my name is GeminiGen.AI. I am a text-to-speech model.",
      "generate_result":null,
      "input_file_path":null,
      "type":"tts-text",
      "used_credit":0,
      "status":1,
      "status_desc":"",
      "status_percentage":50,
      "error_code":"",
      "error_message":"",
      "rating":"",
      "rating_content":"",
      "custom_prompt":null,
      "created_at":"2025-09-11T12:08:15",
      "updated_at":null,
      "file_size":0,
      "file_password":"",
      "expired_at":null,
      "inference_type":"gemini_voice",
      "name":"Hello, my name is GeminiGen.AI. I am a text-to-spe",
      "created_by":"API",
      "is_premium_credit":true,
      "emotion":null,
      "note":"logged-in user: 20, plan_id PP0001",
      "estimated_credit":120,
      "ai_credit":0,
      "media_type":"audio",
      "service_mode":null
   }
}
```

### Response Attributes

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The result of the text-to-speech conversion.

`result.uuid` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the conversion.

`result.voices` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

An array of voice objects used for the conversion. Each voice object contains a name and voice details with id and name.

`result.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech.

`result.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion.

`result.tts_input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text that was converted into speech.

`result.estimated_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The estimated number of credits used for the conversion.

`result.used_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The actual number of credits used for the conversion.

`result.status` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The status of the conversion. Possible values are:

<!-- CONVERTING = 1
JOINING_AUDIO = 12
MERGING_AUDIO = 13
DOWNLOADING_AUDIO = 14
REWORKING = 11
COMPLETED = 2
ERROR = 3 -->

- `1`: Converting
- `2`: Completed
- `3`: Error
- `11`: Reworking
- `12`: Joining Audio
- `13`: Merging Audio
- `14`: Downloading Audio

`result.status_percentage` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The percentage of the conversion that has been completed.

`result.error_message` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The error message, if any.

`result.speaker_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the speaker.

`result.created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was created.

`result.updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was last updated.
